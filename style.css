/* 全局平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 顶部导航栏 */
.top-nav {
    background-color: #4b2e83;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s, padding 0.3s;
}

.nav-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: padding 0.3s;
}

.nav-title a {
    color: #2c5aa0;
    font-weight: bold;
    text-decoration: none;
    white-space: nowrap;
    /* 移除移动端点击时的白色背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.nav-title a:active {
    background-color: transparent !important;
}

.nav-title a:focus {
    outline: none;
    background-color: transparent !important;
}

.nav-links {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
}

/* 汉堡菜单按钮样式 */
.mobile-menu-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 8px;
    /* 移除移动端点击时的背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.mobile-menu-toggle i {
    color: #2c5aa0;
    transition: transform 0.3s ease;
}

.mobile-menu-toggle:hover i {
    transform: scale(1.1);
}

/* 移动端侧边栏菜单样式 */
.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    right: -300px; /* 初始位置在屏幕右侧外 */
    width: 300px;
    height: 100vh;
    background-color: #f2f2f2;
    z-index: 999;
    transition: right 0.3s ease-in-out;
    overflow-y: auto;
    padding-top: 0; /* 移除顶部padding，因为有头部了 */
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav.active {
    right: 0; /* 激活时滑入屏幕 */
}

/* 移动端菜单遮罩层 */
.mobile-nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.mobile-nav-overlay.active {
    display: block;
    opacity: 1;
}

/* 防止页面滚动当菜单打开时 */
body.mobile-menu-open {
    overflow: hidden;
}

/* 移动端导航栏个人资料区域 */
.mobile-nav-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    background-color: #f2f2f2;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    min-height: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mobile-nav-avatar {
    flex-shrink: 0;
    margin-bottom: 15px;
    animation: fadeInScale 0.5s ease-out both;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.mobile-nav-avatar img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.mobile-nav-avatar img:hover {
    transform: scale(1.05);
}

.mobile-nav-info {
    color: #333;
    text-align: center;
    animation: fadeInUp 0.5s ease-out 0.2s both;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-name {
    font-weight: bold;
    margin: 0 0 8px 0;
    color: #222;
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.mobile-nav-title {
    margin: 0 0 4px 0;
    color: #555;
    line-height: 1.3;
}

.mobile-nav-affiliation {
    margin: 5px;
    color: #666;
    line-height: 1.3;
}

.mobile-nav-close {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s;
    /* 移除移动端点击时的背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.mobile-nav-close:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.mobile-nav-close i {
    color: #333;
}



.mobile-nav a {
    display: flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    padding: 20px 25px;
    transition: background-color 0.3s;
    /* 移除移动端点击时的背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.mobile-nav a:last-child {
    border-bottom: none;
}

.mobile-nav a:hover {
    text-decoration: none;
    background-color: rgba(0, 0, 0, 0.05);
}

.mobile-nav a:active {
    background-color: transparent !important;
}

.mobile-nav a:focus {
    outline: none;
    background-color: transparent !important;
}

/* 导航链接样式 */
.nav-links a {
    color: #2c5aa0;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    white-space: nowrap;
    /* 移除移动端点击时的白色背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.nav-links a:hover {
    text-decoration: none;
}

.nav-links a:active {
    background-color: transparent !important;
}

.nav-links a:focus {
    outline: none;
    background-color: transparent !important;
}

/* 导航栏图标样式 */
.nav-links a i {
    margin-right: 6px;
}

.mobile-nav a i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

/* 移动端导航栏样式 */
@media (max-width: 900px) {
    .top-nav {
        background-color: #4b2e83;
    }

    .nav-container {
        flex-direction: row;
        padding: 8px 15px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
    }

    .nav-title {
        flex-shrink: 0;
    }

    /* 隐藏桌面端导航链接 */
    .desktop-nav {
        display: none !important;
    }

    /* 显示汉堡菜单按钮 */
    .mobile-menu-toggle {
        display: flex !important;
    }

    /* 显示移动端侧边栏菜单 */
    .mobile-nav {
        display: block;
    }

    /* 显示移动端菜单遮罩层 */
    .mobile-nav-overlay {
        display: none;
    }

    /* 移动端个人资料区域样式调整 */
    .mobile-nav-profile {
        padding: 20px;
        min-height: 0;
    }

    .mobile-nav-avatar img {
        width: 70px;
        height: 70px;
    }

    .mobile-nav-avatar {
        margin-bottom: 12px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 8px 10px;
    }

    /* 移动端侧边栏在小屏幕上的样式调整 */
    .mobile-nav {
        width: 250px;
        right: -250px;
    }

    .mobile-nav a {
        padding: 16px 18px;
    }

    .mobile-nav-profile {
        padding: 20px 15px;
        min-height: 0;
    }

    .mobile-nav-avatar img {
        width: 120px;
        height: 120px;
    }

    .mobile-nav-avatar {
        margin-bottom: 15px;
    }
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-size: 14.4px;
}

body {
    font-family: "Open Sans", Segoe, "Segoe UI", "Lucida Sans Unicode", "Lucida Grande", "Avenir", "Seravek", "Ubuntu", "DejaVu Sans", "Trebuchet MS", Verdana, Arial, sans-serif;
    line-height: 1.6;
    background-color: #fff;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(to right, rgba(240, 240, 235, 0.4) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(240, 240, 235, 0.4) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: -1;
    pointer-events: none;
}

/* 调整container顶部内边距，为固定导航栏腾出空间 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 80px 20px 20px;
}

/* Main Layout - Two Column */
.main-layout {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

/* Left Sidebar */
.left-sidebar {
    flex: 0 0 280px;
    position: sticky;
    top: 100px;
}

.profile-section {
    text-align: center;
    padding: 30px 20px;
    border-radius: 8px;
}

.profile-photo {
    margin-bottom: 20px;
}

.profile-photo img {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-name {
    font-weight: bold;
    margin: 15px 0 8px 0;
    color: #333;
}

.profile-title {
    color: #666;
    margin: 5px 0;
    text-align: center;
}

.profile-affiliation {
    color: #666;
    margin: 3px 0;
    line-height: 1.4;
    text-align: center;
}

.profile-email {
    color: #666;
    margin: 8px 0;
}

.social-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 20px;
    font-size: 14px;
    line-height: 1.5;
}

.social-icons a {
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}
.social-icons a:hover {
    text-decoration: underline;
}
.social-icons .separator {
    color: #999;
    font-weight: normal;
    user-select: none;
}
/* Right Content */
.right-content {
    flex: 1;
    min-width: 0;
    margin-left: 20px; /* 为时间线标签留出空间 */
}

.right-content section {
    margin-bottom: 40px;
}

.right-content h2 {
    color: #2c5aa0;
    margin-bottom: 15px;
    padding-bottom: 5px;
}

/* About Section */
.about-section p {
    line-height: 1.6;
    color: #000;
    text-align: justify;
}

/* 小屏幕设备优化：避免两端对齐导致的文字间距过大问题 */
@media (max-width: 768px) {
    .about-section p {
        text-align: left;
    }
}

/* Research Interests */
.research-interests-section ul {
    list-style: none;
    padding: 0;
}

.research-interests-section li {
    margin-bottom: 8px;
    line-height: 1.5;
    color: #555;
}

.research-interests-section li strong {
    color: #2c5aa0;
}

/* News Section */
.news-list {
    max-height: none;
    overflow: visible;
}

.news-item {
    display: flex;
    margin-bottom: 10px;
    line-height: 1.5;
}

.news-date {
    flex: 0 0 auto;
    color: #2c5aa0;
    font-weight: bold;
    margin-right: 10px;
}

.news-content {
    flex: 1;
    color: #000;
}
.publication-item {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    align-items: center;
}

.publication-item:last-child {
    border-bottom: none;
}

.pub-image {
    flex: 0 0 284px;
    width: 284px;
    height: 156px;
    align-self: center;
    position: relative;
}

.pub-image img:not(.badge-svg) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pub-content {
    flex: 1;
}

.pub-content h3 {
    font-weight: bold;
    margin-bottom: 6px;
    color: #000;
    line-height: 1.3;
}

.authors {
    color: #000;
    margin-bottom: 4px;
    font-style: italic;
}

.first-author{
    color: #000;
    font-style: italic;
}
.venue {
    margin-bottom: 6px;
}

.pub-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pub-links a {
    color: #2c5aa0;
    text-decoration: none;
}

.pub-links a:hover {
    text-decoration: underline;
}

/* Projects Section */
.projects-list {
    margin: 0;
}

.project-item {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    align-items: center;
}

.project-item:last-child {
    border-bottom: none;
}

.project-image {
    flex: 0 0 284px;
    width: 284px;
    height: 156px;
    align-self: center;
    position: relative;
}

.project-image img:not(.badge-svg) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.project-content {
    flex: 1;
}

.project-content h3 {
    font-weight: bold;
    margin-bottom: 6px;
    color: #000;
    line-height: 1.3;
}

.project-description {
    color: #000;
    margin-bottom: 6px;
}

.project-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

.project-links a {
    color: #2c5aa0;
    text-decoration: none;
}

.project-links a:hover {
    text-decoration: underline;
}

.project-links img {
    vertical-align: middle;
}

/* Talks Section */
.talks-list {
    margin: 0;
}

.talk-item {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.talk-item:last-child {
    border-bottom: none;
}

.talk-image {
    flex: 0 0 284px;
    width: 284px;
    height: 156px;
    align-self: center;
    position: relative;
}

.talk-image img:not(.badge-svg) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Badge styles for publications, projects, and talks */
.image-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    border-radius: 0;
    font-weight: 600;
    font-size: 12px;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background-color: #2c5aa0;
    /* 文字垂直对齐 */
    display: flex;
    align-items: center;
}

.badge-publication {
    height: 18px;
    background-color: #002D72;

}

/* SVG Badge styles */
.badge-svg {
    position: absolute;
    top: 8px;
    left: 8px;
    height: 20px;
    width: auto;
    max-width: 80px;
    z-index: 10;
    background: none !important;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    object-fit: contain;
}

/* Project部分的SVG badge特殊样式 - 稍微大一些 */
.project-image .badge-svg {
    height: 32px !important;
    width: auto !important;
    max-width: 95px !important;
}

.badge-project {
    height: 18px;
    background-color: #002D72;
}

.badge-talk {
    height: 18px;
    background-color: #002D72;
}

.talk-content {
    flex: 1;
}

.talk-content h3 {
    font-weight: bold;
    margin-bottom: 6px;
    color: #000;
    line-height: 1.3;
}

.talk-venue {
    margin-bottom: 4px;
}

.talk-description {
    color: #000;
    margin-bottom: 6px;
}

.talk-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.talk-links a {
    color: #2c5aa0;
    text-decoration: none;
}

.talk-links a:hover {
    text-decoration: underline;
}

/* Education Section - Reference Style */
.education-list {
    margin: 0;
    padding: 0;
}

.education-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 15px 0;
}

.education-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.edu-logo {
    flex-shrink: 0;
    margin-right: 20px;
}

.university-logo {
    width: 50px;
    height: 50px;
    object-fit: contain;
}

.edu-content {
    flex: 1;
    margin-right: 20px;
}

.edu-institution {
    font-weight: bold;
    color: #000;
    margin-bottom: 4px;
    font-size: 14.4px;
}

.edu-department {
    color: #000;
    margin-bottom: 4px;
    font-size: 14.4px;
}

.edu-degree {
    color: #000;
    font-weight: 500;
    font-size: 14.4px;
}

.edu-period {
    flex-shrink: 0;
    color: #000;
    font-size: 14.4px;
    text-align: right;
    min-width: 120px;
}

/* 默认隐藏移动端日期 */
.mobile-period {
    display: none;
}

/* 显示桌面端日期 */
.desktop-period {
    display: block;
}

h2 {
    color: #000;
    padding-bottom: 10px;
    font-size: 16.848px;
}

p {
    margin-bottom: 10px;
    text-align: justify;
}
a {
    color: #2c5aa0;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

strong {
    font-weight: bold;
}

/* Header Section */
.main-header {
    margin-bottom: 40px;
}

.header-content {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    margin-bottom: 20px;
}

.profile-photo {
    flex: 0 0 230px;
}

.profile-photo img {
    width: 230px;
    height: 230px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.profile-photo img:hover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2), 0 6px 12px rgba(0, 0, 0, 0.15);
}

.profile-content {
    flex: 1;
}

.profile-content h1 {
    margin-bottom: 5px;
    color: #000;
}

.title {
    margin-bottom: 5px;
    color: #333;
}

.affiliation {
    margin-bottom: 5px;
    color: #333;
    line-height: 1.4;
}

.email {
    margin-bottom: 10px;
    color: #333;
}

.social-links {
    margin-bottom: 0;
}

.social-links a {
    color: #673ab7;
    text-decoration: none;
    margin-right: 3px;
}

.bio-content {
    margin-top: 0;
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
}

.bio {
    margin-bottom: 15px;
    text-align: justify;
    line-height: 1.5;
}

/* 小屏幕设备优化：避免两端对齐导致的文字间距过大问题 */
@media (max-width: 768px) {
    .bio {
        text-align: left;
    }
}

.looking {
    color: #cc0000;
    margin-bottom: 15px;
}

.student-note {
    margin-bottom: 15px;

}

/* 新闻部分滚动样式 */
.news-section {
    margin-bottom: 30px;

}

.news-list {
    max-height: 500px;
    /* 设置最大高度，超出部分显示滚动条 */
    overflow-y: auto;
    /* 垂直滚动 */
    overflow-x: auto;
    /* 整体水平滚动 */
    border-radius: 5px;
    margin-left: 0;
    white-space: nowrap;
    /* 防止文本换行 */
}

.news-item {
    display: flex;
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.news-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.news-date {
    flex: 0 0 auto;
    /* 改为自动宽度 */
    color: #2c5aa0;
    font-weight: bold;
    padding-right: 5px;
    /* 添加少量右内边距 */
}

.news-content {
    flex: 1;
    padding-right: 10px;
    margin-left: 0;
    /* 移除左边距 */
    overflow: visible;
    /* 移除单独的水平滚动 */
}


/* Publications Section */
.publications-section {
    margin-bottom: 30px;
}

.publications-section h2 {
    margin-bottom: 15px;
}

.publications-section h2 #show-selected-link,
.publications-section h2 #show-all-link {
    transition: color 0.3s ease, text-decoration 0.3s ease;
    cursor: pointer;
    color: #000000;
    font-weight: 600;
}

/* 默认状态：show all by date 高亮 */
.publications-section h2 #show-all-link:not(.inactive) {
    color: #4b2e83;
    text-decoration: underline;
}

.publications-section h2 #show-selected-link:hover,
.publications-section h2 #show-all-link:hover {
    text-decoration: underline;
}

.publications-section h2 #show-selected-link.active,
.publications-section h2 #show-all-link.active {
    color: #4b2e83;
    text-decoration: underline;
}

/* 非活跃状态 */
.publications-section h2 #show-selected-link.inactive,
.publications-section h2 #show-all-link.inactive {
    color: #000000;
    text-decoration: none;
}

/* Education Section */
.education-section {
    margin-bottom: 30px;
}



/* Footer */
.main-footer {
    text-align: center;
    padding-top: 20px;
    width: 100%;
}

.visitor-counter {
    display: inline-block;
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
}

.footer-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
}

.footer-follow {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    color: #666;
}

.footer-links a {
    color: #666;
    text-decoration: none;
}

.footer-links a:hover {
    text-decoration: underline;
    color: #333;
}

.footer-info {
    margin-top: 10px;
    text-align: center;
    width: 100%;
}

.footer-info p {
    color: #000;
    margin: 0 auto;
    text-align: center;
    line-height: 1.5;
    max-width: 100%;
    word-wrap: break-word;
}

/* Citation Modal */
.citation-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.citation-modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 15px;
    border: 1px solid #ddd;
    width: 80%;
    max-width: 700px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
}

.citation-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #666;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.citation-close:hover {
    color: #000;
}

.citation-text-container {
    background-color: #f8f8f8;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
    max-height: 300px;
    overflow-y: auto;
}

#citation-text {
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    line-height: 1.5;
}

.copy-citation-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.copy-citation-btn:active {
    background-color: #3e8e41;
}

.copy-citation-link {
    display: inline-block;
    color: #673ab7;
    text-decoration: underline;
    cursor: pointer;
}

.copy-citation-link:hover {
    color: #5e35b1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        max-width: 95%;
        padding: 80px 15px 20px;
    }

    .main-layout {
        gap: 30px;
    }

    .left-sidebar {
        flex: 0 0 280px;
    }

    .profile-photo img {
        width: 140px;
        height: 140px;
    }

    /* 隐藏publications、projects、talks部分的图片 */
    .pub-image,
    .project-image,
    .talk-image {
        display: none;
    }
}

/* 特定宽度820px左右的布局调整 */
@media (max-width: 900px) and (min-width: 769px) {
    .publication-item,
    .project-item,
    .talk-item {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 10px;
        align-items: stretch; /* 确保子元素拉伸到相同宽度 */
        text-align: left;
    }

    .pub-image,
    .project-image,
    .talk-image {
        width: 100%;
        height: auto;
        flex: none; /* 防止flex收缩 */
        margin: 0; /* 移除所有margin */
        padding: 0; /* 移除所有padding */
        box-sizing: border-box; /* 确保box-sizing一致 */
    }

    .pub-image img:not(.badge-svg),
    .project-image img:not(.badge-svg),
    .talk-image img:not(.badge-svg) {
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block; /* 移除img默认的inline间距 */
    }

    .pub-content,
    .project-content,
    .talk-content {
        width: 100%;
        flex: none; /* 防止flex收缩 */
        margin: 0; /* 移除所有margin */
        padding: 0; /* 移除所有padding */
        box-sizing: border-box; /* 确保box-sizing一致 */
        text-align: left;
    }
}


@media (max-width: 900px) {
    .main-layout {
        flex-direction: column;
        gap: 20px;
    }

    .left-sidebar {
        flex: none;
        position: static;
        width: 100%;
    }

    .profile-section {
        padding: 20px 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 0;
        text-align: center;
    }

    .profile-photo img {
        width: 150px;
        height: 150px;
    }


    .right-content {
        width: 100%;
        margin-left: 0; /* 移动端取消左边距 */
    }

    .publication-item,
    .project-item,
    .talk-item {
        gap: 15px;
        text-align: center;

    }

    .pub-image,
    .project-image,
    .talk-image {
        flex: none;
        width: 100%;
        max-width: 400px;
        height: auto;
        margin: 0 auto;
    }

    .pub-image img:not(.badge-svg),
    .project-image img:not(.badge-svg),
    .talk-image img:not(.badge-svg) {
        width: 100%;
        height: auto;
        aspect-ratio: 3/2;
    }

    .pub-content,
    .project-content,
    .talk-content {
        text-align: left;
    }

    .education-list {
        padding-left: 0; /* 移动端取消左边距，与标题对齐 */
    }

    .education-list::before {
        left: 10px;
    }

    .education-item {
        padding-left: 0; /* 移动端取消左边距，与标题对齐 */
    }

    .education-item::before {
        left: 4px;
        top: 50%;
        transform: translateY(-50%); /* 垂直居中 */
        width: 10px;
        height: 10px;
    }

    .edu-period {
        position: static;
        width: auto;
        text-align: left;
        margin-bottom: 8px;
        margin-left: 0; /* 确保与Education标题对齐 */
        display: inline-block;
    }

    .edu-info-container {
        flex-direction: row;
        align-items: center;
        text-align: left;
        gap: 15px;
    }

    .university-logo {
        width: 80px;
        height: 80px;
    }

    .news-item {
        flex-direction: column;
        gap: 5px;
    }

    .news-date {
        margin-right: 0;
    }
}

@media (max-width: 900px) {

    .top-nav,
    .top-nav.scrolled {
        background-color: #fff;
    }

    .container {
        padding: 60px 15px 20px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .profile-photo {
        margin: 0;
        width: 230px;
    }

    .profile-photo img {
        width: 230px;
        height: 230px;
        margin: 0;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease;
    }

    .profile-content {
        text-align: left;
        margin-top: 10px;
        padding-left: 0;
    }

    .title,
    .affiliation,
    .email {
        text-align: left;
    }

    .social-links {
        text-align: left;
        margin-bottom: 15px;
    }

    .bio-content {
        margin-top: 20px;
        padding-left: 0;
    }

    .bio,
    .looking,
    .student-note {
        text-align: left;
        line-height: 1.5;
        margin-bottom: 10px;
        padding-left: 0;
    }

    .research-interests {
        text-align: left;
        line-height: 1.5;
        margin-bottom: 10px;
    }

    .contact-links {
        line-height: 1.5;
        text-align: center;
        margin-bottom: 15px;
        order: 3;
    }

    .contact-links a {
        margin-right: 8px;
    }

    .publication-item,
    .project-item {
        gap: 12px;
        margin-bottom: 10px;
    }

    .publication-item:last-child,
    .project-item:last-child {
        border-bottom: none;
    }

    .research-projects-section .projects-list {
        margin-left: 20px;
    }

    .pub-image,
    .project-image,
    .talk-image {
        align-self: center;
        flex: none;
        position: relative;
        margin-bottom: 10px;
        width: 100%;
        max-width: 400px;
        height: auto;
        aspect-ratio: 284/156;
    }

    .pub-image img:not(.badge-svg),
    .project-image img:not(.badge-svg),
    .talk-image img:not(.badge-svg) {
        width: 100%;
        height: 100%;
        object-fit: cover;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    }

    .pub-badge,
    .project-badge {
        padding: 3px 6px;
    }

    .education-item {
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 20px;
        padding: 15px 0;
        gap: 12px;
    }

    .edu-logo {
        margin-right: 0;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .university-logo {
        width: 32px;
        height: 32px;
    }

    .edu-content {
        margin-right: 0;
        margin-bottom: 0;
        margin-top: 0;
        flex: 1;
    }

    .edu-institution {
        font-size: 14.4px;
        margin-bottom: 2px;
    }

    .edu-department {
        font-size: 12px;
        margin-bottom: 2px;
    }

    .edu-degree {
        font-size: 12px;
        margin-bottom: 0;
    }

    .edu-period.desktop-period {
        display: none;
    }

    .edu-period.mobile-period {
        display: block;
        text-align: left;
        min-width: auto;
        margin-bottom: 0;
        margin-left: 0;
        color: #000;
        font-weight: normal;
        font-size: 14.4px;
        margin-top: 4px;
    }

    .edu-content h3 {
        margin-top: 0;
        margin-bottom: 1px;
        line-height: 1.2;
    }

    .edu-content p {
        margin-bottom: 1px;
    }

    h1 {
        margin-bottom: 10px;
        line-height: 1.2;
    }

    h2 {
        margin: 20px 0 8px 0;
        line-height: 1.2;
    }

    h3 {
        line-height: 1.3;
    }

    .news-item {
        flex-direction: column;
        gap: 2px;
        margin-bottom: 10px;
    }

    .news-date {
        min-width: auto;
        color: #2c5aa0;
        font-weight: bold;
    }

    .news-content {
        line-height: 1.3;
    }

    .pub-links a,
    .project-links a {
        margin-right: 8px;
    }

    .citation-modal-content {
        margin: 15% auto;
        width: 90%;
        padding: 20px;
    }

    .citation-text-container {
        max-height: 250px;
        padding: 12px;
    }


    .visitor-counter {
        text-align: center;
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .footer-links {
        margin: 10px 0;
        text-align: center;
    }

    .footer-follow {
        gap: 8px;
        justify-content: center;
    }

    /* 中等屏幕页脚优化 */
    .main-footer {
        text-align: center !important;
        padding: 20px 15px;
    }

    .footer-info {
        text-align: center !important;
        margin-top: 15px;
    }

    .footer-info p {
        text-align: center !important;
        font-size: 13px;
        line-height: 1.5;
        margin: 0 auto;
        word-wrap: break-word;
    }

    /* 移动端学校图标调整 */
    .bio img[src*="icon-title"] {
        width: 18px !important;
        height: 18px !important;
        margin-left: 3px !important;
    }

    .bio img[src*="scuec.png"] {
        width: 65px !important;
        height: 25px !important;
        margin-left: 3px !important;
    }

    .contact-links a {
        padding: 0 !important;
        margin: 0 8px 0 0 !important;
        background-color: transparent !important;
        border-radius: 0 !important;
    }

    .contact-links a:hover {
        background-color: transparent !important;
    }

    .pub-links {
        gap: 8px;
    }

    .pub-links a,
    .project-links a {
        padding: 0 !important;
        margin: 0 !important;
        background-color: transparent !important;
        border-radius: 0 !important;
    }

    .pub-links a:hover,
    .project-links a:hover {
        background-color: transparent !important;
    }


    .news-item {
        background-color: transparent !important;
        border-radius: 0 !important;
        padding: 0 !important;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 50px 10px 15px;
    }


    h1 {
        line-height: 1.1;
        margin-bottom: 8px;
        text-align: left;
    }

    h2 {
        margin: 15px 0 6px 0;
        line-height: 1.1;
    }

    h3 {
        line-height: 1.3;
        margin-bottom: 8px;
    }

    .profile-section {
        padding: 20px 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 0;
        text-align: center;
    }

    .profile-photo {
        width: 200px;
        margin-bottom: 15px;
    }

    .profile-photo img {
        width: 200px;
        height: 200px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease;
    }

    .profile-content h1 {
        margin-bottom: 5px;
    }


    .bio,
    .looking,
    .student-note {
        line-height: 1.4;
        text-align: left;
    }

    .contact-links {
        text-align: center;
        margin-bottom: 15px;
    }

    .bio,
    .research-interests {
        line-height: 1.4;
        margin-bottom: 8px;
        text-align: left;
    }

    .contact-links a {
        margin-right: 6px;
    }

    .news-list,
    .publications-list,
    .research-projects-section .projects-list,
    .projects-list {
        margin-left: 5px;
    }

    .education-list {
        margin-left: 0; /* 教育列表与标题对齐 */
    }

    .news-item {
        margin-bottom: 8px;
    }

    .news-date {
        margin-bottom: 1px;
    }

    .news-content {
        line-height: 1.2;
    }

    .pub-image,
    .project-image,
    .talk-image {
        max-width: 100%;
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
    }

    .pub-image img:not(.badge-svg),
    .project-image img:not(.badge-svg),
    .talk-image img:not(.badge-svg) {
        width: 100%;
        height: 100%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }

    .pub-badge,
    .project-badge {
        padding: 2px 5px;
    }

    .pub-content,
    .project-content {
        padding: 8px 0;
        text-align: left;
        display: block;
    }

    .authors {
        margin-bottom: 4px;
    }

    .venue {
        margin-bottom: 6px;
        line-height: 1.4;
    }

    .pub-description,
    .project-content p {
        line-height: 1.4;
    }

    .pub-links {
        gap: 6px;
    }

    .pub-links a,
    .project-links a {
        margin-right: 0;
        line-height: 1.4;
    }

    .education-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;
        padding: 10px 0;
        margin-left: 0;
    }

    .edu-logo {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .university-logo {
        width: 35px;
        height: 35px;
    }

    .edu-content {
        margin-right: 0;
        margin-bottom: 6px;
        margin-top: 0;
        padding: 0;
    }

    .edu-period {
        text-align: left;
        min-width: auto;
        margin-bottom: 0;
        margin-left: 0;
        display: block;
    }

    .visitor-counter {
        margin-bottom: 5px;
        text-align: center;
        display: flex;
        justify-content: center;
        width: 100%;
    }

    /* 小屏幕页脚优化 */
    .main-footer {
        text-align: center;
        padding: 15px 5px;
    }

    .footer-info {
        text-align: center;
        margin-top: 10px;
    }

    .footer-info p {
        text-align: center;
        font-size: 14.4px;
        line-height: 1.5;
        margin: 0 auto;
        padding: 0 5px;
        word-wrap: break-word;
        hyphens: auto;
    }

    /* 移动端学校图标调整 */
    .bio img[src*="icon-title"] {
        width: 15px !important;
        height: 15px !important;
        margin-left: 2px !important;
    }

    .bio img[src*="scuec.png"] {
        width: 60px !important;
        height: 20px !important;
        margin-left: 2px !important;
    }

    .citation-modal-content {
        margin: 20% auto;
        width: 95%;
        padding: 15px;
    }

    .citation-text-container {
        max-height: 200px;
        padding: 10px;
    }

    .citation-close {
        top: 10px;
        right: 15px;
    }

    /* 改善publication内容在小屏幕上的显示 */
    .pub-content h3 {
        line-height: 1.3;
        margin-bottom: 8px;
        word-wrap: break-word;
        hyphens: auto;
    }

    .pub-links {
        margin-top: 8px;
        gap: 8px;
    }
}

/* Print Styles */
@media print {
    .visitor-counter {
        display: none;
    }

    .citation-modal {
        display: none;
    }

    a {
        color: #000;
        text-decoration: underline;
    }
}

/* 滚动和交互体验优化 */
@media (max-width: 900px) {
    html {
        scroll-behavior: smooth;
    }

    body {
        -webkit-overflow-scrolling: touch;
    }

    /* 优化文本选择 */
    p,
    h1,
    h2,
    h3 {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Visitor Counter 溢出处理 */
    .visitor-counter {
        overflow-x: auto;
        overflow-y: hidden;
        width: 100%;
        display: flex;
        justify-content: center;
        text-align: center;
    }

    .visitor-counter>* {
        max-width: 100%;
    }

    /* 移动端页脚居中对齐 */
    .main-footer {
        text-align: center !important;
        padding: 20px 10px;
    }

    .footer-info {
        text-align: center !important;
        margin-top: 15px;
    }

    .footer-info p {
        text-align: center !important;
        margin: 0 auto;
        max-width: 100%;
        word-wrap: break-word;
        line-height: 1.6;
    }
}

/* 确保导航栏在所有情况下都保持深蓝色背景 */
.top-nav,
.top-nav.scrolled {
    background-color: #fff !important;
}

/* 会议或期刊等级样式 */
.venue-rank {
    display: inline-block;
    color: red;
    font-weight: bold;
    /* 斜体 */
    font-style: italic;
    margin: 0 5px;
}

/* 回到顶部按钮样式 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: white;
    color: #4b2e83;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000;
    /* 移除移动端点击时的背景高亮效果 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #f5f5f5;
}

.back-to-top:active {
    background-color: white !important;
    transform: scale(0.95);
}

.back-to-top:focus {
    outline: none;
    background-color: white !important;
}

/* 个人资料内容样式更新 */
.profile-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
    width: 100%;
}

.profile-name {
    font-weight: bold;
    margin: 10px 0;
    color: #2C5AA0;
    text-align: center;
    width: 100%;
}

.profile-title {
    margin: 5px 0;
    color: #555;
    text-align: center;
    width: 100%;
}

.profile-affiliation {
    margin: 5px 0;
    color: #000;
    text-align: center;
    width: 100%;
    font-size: 16px;
}

.profile-location {
    margin: 5px 0;
    color: #666;
    text-align: center;
    width: 100%;
}

.profile-email {
    margin: 5px 0;
    color: #666;
    text-align: center;
    width: 100%;
}

.social-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
    gap: 8px;
    font-size: 14px;
}

.social-icons a {
    color: #2c5aa0;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
}
/* 移动端适配 */
@media (max-width: 900px) {
    .social-icons {
        gap: 6px;
        font-size: 13px;
    }

    .social-icons a {
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .social-icons {
        gap: 3px;
        font-size: 12px;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .social-icons a {
        padding: 2px 4px;
    }

    .social-icons .separator {
        font-size: 10px;
    }

    /* 移动端教育部分样式 */
    .education-item {
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 15px;
        padding: 12px 5px;
        gap: 10px;
    }

    .edu-logo {
        margin-right: 0;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .university-logo {
        width: 28px;
        height: 28px;
    }

    .edu-content {
        margin-right: 0;
        margin-bottom: 0;
        flex: 1;
    }

    .edu-institution {
        font-size: 14.4px;
        margin-bottom: 2px;
    }

    .edu-department {
        font-size: 14.4px;
        margin-bottom: 1px;
    }

    .edu-degree {
        font-size: 14.4px;
        margin-bottom: 0;
    }

    .edu-period.desktop-period {
        display: none;
    }

    .edu-period.mobile-period {
        display: block;
        text-align: left;
        min-width: auto;
        font-size: 14.4px;
        color: #000;
        font-weight: normal;
        margin-top: 3px;
    }
}

/* 个人资料容器样式 */
.profile-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    text-align: center;
}

.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 20px;
    text-align: center;
}

/* 主标题样式 */
.main-header {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 小屏幕设备文字对齐优化 - 统一处理所有两端对齐的文本 */
@media (max-width: 768px) {
    /* 特殊处理长文本内容 */
    .bio,
    .looking,
    .student-note,
    .research-interests {
        text-align: left !important;
        text-align-last: left !important;
    }

    /* 强制保持个人资料信息居中对齐 */
    .profile-section,
    .profile-section *,
    .profile-title,
    .profile-affiliation,
    .profile-location,
    .profile-email,
    .profile-name {
        text-align: center !important;
    }

    /* 移动端隐藏返回顶部按钮 */
    .back-to-top {
        display: none !important;
    }
}